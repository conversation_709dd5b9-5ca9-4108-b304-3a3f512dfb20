#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gestion du stockage pour le système RAG Confluence.
Supporte le stockage sur système de fichiers local et Google Cloud Storage (GCS).
"""

import os
import json
import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, BinaryIO, List
from datetime import datetime
import hashlib

from .thread_pool_manager import get_thread_pool_manager

# Importation conditionnelle pour GCS
try:
    from google.cloud import storage
    from google.cloud.exceptions import GoogleCloudError
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False


class StorageProvider(ABC):
    """Interface abstraite pour les fournisseurs de stockage."""

    @abstractmethod
    async def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu et retourne son chemin de stockage."""
        pass

    @abstractmethod
    async def save_attachment(self, content_id: str, attachment_id: str,
                             file_name: str, data: bytes) -> str:
        """Sauvegarde une pièce jointe et retourne son chemin de stockage."""
        pass

    @abstractmethod
    async def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID."""
        pass

    @abstractmethod
    async def get_attachment(self, content_id: str, attachment_id: str) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID."""
        pass

    @abstractmethod
    async def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés."""
        pass

    @abstractmethod
    async def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné."""
        pass


class FileSystemStorage(StorageProvider):
    """Fournisseur de stockage utilisant le système de fichiers local."""

    def __init__(self, base_dir: str = "output_data_dir"):
        """Initialise le stockage avec le répertoire de base spécifié."""
        self.base_dir = base_dir
        self.logger = logging.getLogger(__name__)

        # Utiliser le gestionnaire de pools de threads centralisé
        self.thread_pool_manager = get_thread_pool_manager()

        # Créer les répertoires nécessaires
        os.makedirs(base_dir, exist_ok=True)
        os.makedirs(os.path.join(base_dir, "contents"), exist_ok=True)
        os.makedirs(os.path.join(base_dir, "attachments"), exist_ok=True)

    async def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu au format JSON."""
        try:
            # Créer le chemin du fichier
            content_dir = os.path.join(self.base_dir, "contents")
            file_path = os.path.join(content_dir, f"{content_id}.json")

            # Sauvegarder les données au format JSON en utilisant le pool I/O optimisé
            await self.thread_pool_manager.run_in_io_pool(
                self._write_json_file, file_path, data
            )

            self.logger.info(f"Contenu {content_id} sauvegardé dans {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du contenu {content_id}: {e}")
            raise

    async def save_attachment(self, content_id: str, attachment_id: str,
                             file_name: str, data: bytes) -> str:
        """Sauvegarde une pièce jointe."""
        try:
            # Créer le répertoire pour les pièces jointes du contenu
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            os.makedirs(attachment_dir, exist_ok=True)

            # Nettoyer le nom de fichier pour éviter les problèmes de chemin
            safe_filename = self._sanitize_filename(file_name)

            # Ajouter l'ID de la pièce jointe au nom du fichier pour éviter les collisions
            file_path = os.path.join(attachment_dir, f"{attachment_id}_{safe_filename}")

            # Sauvegarder les données binaires
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self._write_binary_file(file_path, data)
            )

            self.logger.info(f"Pièce jointe {attachment_id} sauvegardée dans {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde de la pièce jointe {attachment_id}: {e}")
            raise

    async def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID."""
        try:
            file_path = os.path.join(self.base_dir, "contents", f"{content_id}.json")
            if not os.path.exists(file_path):
                return None

            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                None,
                lambda: self._read_json_file(file_path)
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du contenu {content_id}: {e}")
            return None

    async def get_attachment(self, content_id: str, attachment_id: str) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return None

            # Trouver le fichier correspondant à l'ID de la pièce jointe
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                None,
                lambda: [f for f in os.listdir(attachment_dir) if f.startswith(f"{attachment_id}_")]
            )

            if not files:
                return None

            file_path = os.path.join(attachment_dir, files[0])
            return await loop.run_in_executor(
                None,
                lambda: self._read_binary_file(file_path)
            )
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de la pièce jointe {attachment_id}: {e}")
            return None

    async def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés."""
        try:
            content_dir = os.path.join(self.base_dir, "contents")
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                None,
                lambda: os.listdir(content_dir)
            )
            return [f.replace(".json", "") for f in files if f.endswith(".json")]
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des contenus: {e}")
            return []

    async def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return []

            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                None,
                lambda: os.listdir(attachment_dir)
            )
            return [f.split("_")[0] for f in files]
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des pièces jointes pour {content_id}: {e}")
            return []

    def _write_json_file(self, file_path: str, data: Dict[str, Any]) -> None:
        """Écrit des données JSON dans un fichier."""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def _read_json_file(self, file_path: str) -> Dict[str, Any]:
        """Lit des données JSON depuis un fichier."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _write_binary_file(self, file_path: str, data: bytes) -> None:
        """Écrit des données binaires dans un fichier."""
        with open(file_path, 'wb') as f:
            f.write(data)

    def _read_binary_file(self, file_path: str) -> bytes:
        """Lit des données binaires depuis un fichier."""
        with open(file_path, 'rb') as f:
            return f.read()

    def _sanitize_filename(self, filename: str) -> str:
        """Nettoie un nom de fichier pour éviter les problèmes de chemin."""
        # Remplacer les caractères problématiques
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename


class GCSStorage(StorageProvider):
    """Fournisseur de stockage utilisant Google Cloud Storage."""

    def __init__(self, bucket_name: str, base_prefix: str = "confluence_rag"):
        """Initialise le stockage avec le bucket et le préfixe spécifiés."""
        if not GCS_AVAILABLE:
            raise ImportError("Le module google-cloud-storage n'est pas installé. "
                             "Installez-le avec 'pip install google-cloud-storage'.")

        self.bucket_name = bucket_name
        self.base_prefix = base_prefix
        self.logger = logging.getLogger(__name__)

        # Initialiser le client GCS
        self.client = storage.Client()
        self.bucket = self.client.bucket(bucket_name)

        # Vérifier que le bucket existe
        if not self.bucket.exists():
            self.logger.warning(f"Le bucket {bucket_name} n'existe pas. Tentative de création...")
            self.bucket.create()

    async def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu au format JSON dans GCS."""
        try:
            # Créer le chemin de l'objet
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            # Convertir les données en JSON
            json_data = json.dumps(data, ensure_ascii=False, indent=2, default=str)

            # Sauvegarder les données
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: blob.upload_from_string(json_data, content_type='application/json')
            )

            self.logger.info(f"Contenu {content_id} sauvegardé dans gs://{self.bucket_name}/{object_path}")
            return f"gs://{self.bucket_name}/{object_path}"
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde du contenu {content_id} dans GCS: {e}")
            raise

    async def save_attachment(self, content_id: str, attachment_id: str,
                             file_name: str, data: bytes) -> str:
        """Sauvegarde une pièce jointe dans GCS."""
        try:
            # Nettoyer le nom de fichier
            safe_filename = self._sanitize_filename(file_name)

            # Créer le chemin de l'objet
            object_path = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_{safe_filename}"
            blob = self.bucket.blob(object_path)

            # Déterminer le type de contenu
            content_type = self._guess_content_type(file_name)

            # Sauvegarder les données
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: blob.upload_from_string(data, content_type=content_type)
            )

            self.logger.info(f"Pièce jointe {attachment_id} sauvegardée dans gs://{self.bucket_name}/{object_path}")
            return f"gs://{self.bucket_name}/{object_path}"
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde de la pièce jointe {attachment_id} dans GCS: {e}")
            raise

    async def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID depuis GCS."""
        try:
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            # Vérifier si l'objet existe
            loop = asyncio.get_event_loop()
            exists = await loop.run_in_executor(None, blob.exists)
            if not exists:
                return None

            # Récupérer les données
            json_data = await loop.run_in_executor(None, blob.download_as_text)
            return json.loads(json_data)
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du contenu {content_id} depuis GCS: {e}")
            return None

    async def get_attachment(self, content_id: str, attachment_id: str) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID depuis GCS."""
        try:
            # Lister les objets pour trouver la pièce jointe
            prefix = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_"
            loop = asyncio.get_event_loop()
            blobs = await loop.run_in_executor(
                None,
                lambda: list(self.bucket.list_blobs(prefix=prefix, max_results=1))
            )

            if not blobs:
                return None

            # Récupérer les données
            return await loop.run_in_executor(None, blobs[0].download_as_bytes)
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de la pièce jointe {attachment_id} depuis GCS: {e}")
            return None

    async def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés dans GCS."""
        try:
            prefix = f"{self.base_prefix}/contents/"
            loop = asyncio.get_event_loop()
            blobs = await loop.run_in_executor(
                None,
                lambda: list(self.bucket.list_blobs(prefix=prefix))
            )

            content_ids = []
            for blob in blobs:
                # Extraire l'ID du contenu du nom de l'objet
                name = blob.name.replace(prefix, "").replace(".json", "")
                if name:
                    content_ids.append(name)

            return content_ids
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des contenus depuis GCS: {e}")
            return []

    async def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné dans GCS."""
        try:
            prefix = f"{self.base_prefix}/attachments/{content_id}/"
            loop = asyncio.get_event_loop()
            blobs = await loop.run_in_executor(
                None,
                lambda: list(self.bucket.list_blobs(prefix=prefix))
            )

            attachment_ids = []
            for blob in blobs:
                # Extraire l'ID de la pièce jointe du nom de l'objet
                name = blob.name.replace(prefix, "")
                if name:
                    attachment_id = name.split("_")[0]
                    if attachment_id not in attachment_ids:
                        attachment_ids.append(attachment_id)

            return attachment_ids
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des pièces jointes pour {content_id} depuis GCS: {e}")
            return []

    def _sanitize_filename(self, filename: str) -> str:
        """Nettoie un nom de fichier pour GCS."""
        # Remplacer les caractères problématiques
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename

    def _guess_content_type(self, filename: str) -> str:
        """Devine le type MIME à partir du nom de fichier."""
        ext = os.path.splitext(filename.lower())[1]
        content_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.txt': 'text/plain',
            '.html': 'text/html',
            '.htm': 'text/html',
            '.md': 'text/markdown',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif'
        }
        return content_types.get(ext, 'application/octet-stream')


def get_storage_provider(storage_type: str, **kwargs) -> StorageProvider:
    """Crée et retourne un fournisseur de stockage selon le type spécifié."""
    if storage_type.lower() == 'filesystem':
        base_dir = kwargs.get('base_dir', 'output_data_dir')
        return FileSystemStorage(base_dir=base_dir)
    elif storage_type.lower() == 'gcs':
        bucket_name = kwargs.get('bucket_name')
        if not bucket_name:
            raise ValueError("Le paramètre 'bucket_name' est requis pour le stockage GCS")
        base_prefix = kwargs.get('base_prefix', 'confluence_rag')
        return GCSStorage(bucket_name=bucket_name, base_prefix=base_prefix)
    else:
        raise ValueError(f"Type de stockage non supporté: {storage_type}")
